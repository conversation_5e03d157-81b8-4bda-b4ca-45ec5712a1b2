# ZF Security - 接口中转服务

基于 Netty 实现的 mTLS 网络协议接口中转服务，提供安全可靠的接口代理功能。

## 功能特性

- 🔐 **mTLS 安全通信**：支持双向 SSL/TLS 认证，确保通信安全
- 🔄 **接口中转**：支持 key-filling 和 key-lock 接口的安全中转
- 📊 **日志记录**：完整记录每次接口调用的详细日志信息
- ⚡ **高性能**：基于 Netty 框架，提供高并发处理能力
- 🎯 **Web 管理**：提供 Web 界面进行系统管理和日志查看

## 技术栈

- **后端框架**：Spring Boot 3.5.0
- **网络框架**：Netty 4.1.104
- **数据库**：MySQL 8.0
- **安全框架**：Spring Security
- **模板引擎**：Thymeleaf
- **构建工具**：Maven

## 快速开始

### 1. 环境准备

- JDK 17+
- MySQL 8.0+
- Maven 3.6+

### 2. 数据库配置

1. 创建数据库：
```sql
CREATE DATABASE zf_security DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 修改 `src/main/resources/application.yml` 中的数据库连接信息：
```yaml
spring:
  datasource:
    url: ************************************************************************************************************************
    username: your_username
    password: your_password
```

### 3. 证书配置

1. 将您的证书文件 `zf.pfx` 放置在 `src/main/resources/certs/` 目录下
2. 确认证书密码为 `hjdz@2025`，或在配置文件中修改密码

如果没有证书文件，系统将自动使用自签名证书进行测试。

### 4. 启动应用

```bash
# 编译项目
mvn clean compile

# 启动应用
mvn spring-boot:run
```

### 5. 访问系统

- **Web 管理界面**：http://localhost:8080
- **Netty 服务器**：https://localhost:8443 (mTLS)

默认登录账户：
- 用户名：`admin`，密码：`admin123`
- 用户名：`user`，密码：`user123`

## 接口说明

### 支持的接口

1. **key-filling 接口**
   - 路径：`/api/key-filling`
   - 方法：POST
   - 功能：密钥填充操作

2. **key-lock 接口**
   - 路径：`/api/key-lock`
   - 方法：POST
   - 功能：密钥锁定操作

### 认证机制

系统使用基于签名的认证机制：

1. **请求头**：
   - `X-App-Key`：应用密钥
   - `X-Timestamp`：时间戳
   - `X-Nonce`：随机数
   - `X-Signature`：签名

2. **签名算法**：
   ```
   signature = MD5(appkey=<appkey>&timestamp=<timestamp>&nonce=<nonce>&interface=<interface>&body=<body>&secret=<secret>)
   ```

## 配置说明

### 主要配置项

```yaml
# Netty服务器配置
netty:
  server:
    port: 8443
    ssl:
      enabled: true
      keystore-path: classpath:certs/zf.pfx
      keystore-password: hjdz@2025
      keystore-type: PKCS12

# 接口中转配置
proxy:
  target:
    base-url: https://api.example.com
  auth:
    app-key: your-app-key
    app-secret: your-app-secret

# 登录配置
login:
  users:
    admin: admin123
    user: user123
```

## 使用示例

### 客户端调用示例

```bash
# 使用 curl 调用 key-filling 接口
curl -k -X POST https://localhost:8443/api/key-filling \
  -H "Content-Type: application/json" \
  -H "X-App-Key: your-app-key" \
  -H "X-Timestamp: 1640995200" \
  -H "X-Nonce: 123456789" \
  -H "X-Signature: calculated-signature" \
  -d '{"keyId":"12345","action":"fill"}'
```

### Java 客户端示例

```java
// 创建 SSL 上下文（忽略证书验证，仅用于测试）
SSLContext sslContext = SSLContext.getInstance("TLS");
sslContext.init(null, new TrustManager[]{new X509TrustManager() {
    public X509Certificate[] getAcceptedIssuers() { return null; }
    public void checkClientTrusted(X509Certificate[] certs, String authType) {}
    public void checkServerTrusted(X509Certificate[] certs, String authType) {}
}}, new SecureRandom());

// 创建 HTTP 客户端
CloseableHttpClient client = HttpClients.custom()
    .setSSLContext(sslContext)
    .setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
    .build();

// 发送请求
HttpPost post = new HttpPost("https://localhost:8443/api/key-filling");
post.setHeader("Content-Type", "application/json");
post.setHeader("X-App-Key", "your-app-key");
// ... 添加其他认证头
post.setEntity(new StringEntity("{\"keyId\":\"12345\",\"action\":\"fill\"}"));

CloseableHttpResponse response = client.execute(post);
```

## 日志管理

系统提供完整的日志记录功能：

1. **日志内容**：
   - 请求时间和响应时间
   - 客户端 IP 地址
   - 请求方法和 URL
   - 请求头和请求体
   - 响应状态码、响应头和响应体
   - 请求耗时
   - 成功/失败状态
   - 错误信息

2. **日志查看**：
   - 通过 Web 管理界面查看
   - 支持按接口类型筛选
   - 支持查看失败记录
   - 实时统计信息

## 开发说明

### 项目结构

```
src/main/java/com/hj/zf_security/
├── config/          # 配置类
├── controller/      # 控制器
├── entity/          # 实体类
├── netty/           # Netty 相关类
├── repository/      # 数据访问层
├── service/         # 服务层
└── util/            # 工具类

src/main/resources/
├── certs/           # 证书文件
├── sql/             # 数据库脚本
├── templates/       # 页面模板
└── application.yml  # 配置文件
```

### 扩展开发

1. **添加新接口**：
   - 在 `ProxyServerHandler` 中添加新的接口路径解析
   - 在 `ProxyService` 中添加对应的转发逻辑

2. **自定义认证**：
   - 修改 `ProxyService` 中的 `addAuthHeaders` 方法
   - 实现自定义的签名算法

3. **扩展日志功能**：
   - 修改 `ProxyLog` 实体类添加新字段
   - 在 `ProxyLogService` 中添加新的查询方法

## 注意事项

1. **生产环境部署**：
   - 使用正式的 CA 签发证书
   - 修改默认的登录账户和密码
   - 配置正确的目标服务器地址
   - 设置合适的日志级别

2. **安全考虑**：
   - 定期更换 app-secret
   - 监控异常请求和失败日志
   - 配置防火墙规则限制访问

3. **性能优化**：
   - 根据实际负载调整 Netty 线程池大小
   - 配置数据库连接池参数
   - 定期清理历史日志数据

## 故障排除

### 常见问题

1. **证书加载失败**：
   - 检查证书文件路径和密码
   - 确认证书格式为 PKCS12

2. **数据库连接失败**：
   - 检查数据库服务是否启动
   - 确认连接参数正确

3. **接口调用失败**：
   - 检查目标服务器是否可达
   - 确认认证参数正确

### 日志查看

- 应用日志：查看控制台输出或日志文件
- 数据库日志：通过 Web 管理界面查看
- Netty 日志：设置日志级别为 DEBUG

## 许可证

本项目仅供学习和测试使用。
