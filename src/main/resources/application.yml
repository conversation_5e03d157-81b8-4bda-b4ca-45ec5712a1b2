spring:
  application:
    name: zf_security
  
  # 数据库配置 (H2 内存数据库用于演示)
  datasource:
    url: jdbc:h2:mem:zf_security;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password:
    driver-class-name: org.h2.Driver

  # H2 控制台配置
  h2:
    console:
      enabled: true
      path: /h2-console
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
  
  # Thymeleaf配置
  thymeleaf:
    cache: false
    prefix: classpath:/templates/
    suffix: .html
    encoding: UTF-8
    mode: HTML

# 服务器配置
server:
  port: 8080

# Netty服务器配置
netty:
  server:
    port: 8443
    ssl:
      enabled: true
      keystore-path: classpath:certs/zf.pfx
      keystore-password: hjdz@2025
      keystore-type: PKCS12

# 接口中转配置
proxy:
  target:
    base-url: https://api.example.com
  auth:
    app-key: your-app-key
    app-secret: your-app-secret

# 登录配置
login:
  users:
    admin: admin123
    user: user123

# 日志配置
logging:
  level:
    com.hj.zf_security: DEBUG
    io.netty: INFO
