# 证书文件说明

请将您的证书文件 `zf.pfx` 放置在此目录下。

证书配置信息：
- 文件名：zf.pfx
- 密码：hjdz@2025
- 类型：PKCS12

如果证书文件不存在，系统将自动使用自签名证书进行测试。

## 生成测试证书（可选）

如果需要生成测试用的自签名证书，可以使用以下命令：

```bash
keytool -genkeypair -alias zf -keyalg RSA -keysize 2048 -storetype PKCS12 -keystore zf.pfx -validity 365 -storepass hjdz@2025 -keypass hjdz@2025 -dname "CN=localhost, OU=ZF Security, O=HJ, L=Beijing, ST=Beijing, C=CN"
```

注意：生产环境请使用正式的CA签发证书。
