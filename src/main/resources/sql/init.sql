-- 创建数据库
CREATE DATABASE IF NOT EXISTS zf_security DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE zf_security;

-- 创建代理日志表
CREATE TABLE IF NOT EXISTS proxy_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    request_time DATETIME NOT NULL COMMENT '请求时间',
    interface_name VARCHAR(100) NOT NULL COMMENT '接口名称',
    client_ip VARCHAR(50) COMMENT '客户端IP',
    request_method VARCHAR(10) COMMENT '请求方法',
    request_url VARCHAR(500) COMMENT '请求URL',
    request_headers TEXT COMMENT '请求头',
    request_body TEXT COMMENT '请求体',
    response_status INT COMMENT '响应状态码',
    response_headers TEXT COMMENT '响应头',
    response_body TEXT COMMENT '响应体',
    response_time DATETIME COMMENT '响应时间',
    duration_ms BIGINT COMMENT '耗时(毫秒)',
    error_message TEXT COMMENT '错误信息',
    success BOOLEAN COMMENT '是否成功',
    INDEX idx_request_time (request_time),
    INDEX idx_interface_name (interface_name),
    INDEX idx_client_ip (client_ip),
    INDEX idx_success (success)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='接口代理日志表';

-- 插入一些示例数据（可选）
INSERT INTO proxy_log (
    request_time, interface_name, client_ip, request_method, request_url,
    request_headers, request_body, response_status, response_headers, response_body,
    response_time, duration_ms, success
) VALUES 
(
    NOW() - INTERVAL 1 HOUR, 'key-filling', '*************', 'POST', '/api/key-filling',
    '{"Content-Type":"application/json","X-App-Key":"test-key"}', 
    '{"keyId":"12345","action":"fill"}',
    200, '{"Content-Type":"application/json"}', 
    '{"success":true,"message":"Key filled successfully"}',
    NOW() - INTERVAL 1 HOUR + INTERVAL 150 MILLISECOND, 150, true
),
(
    NOW() - INTERVAL 30 MINUTE, 'key-lock', '*************', 'POST', '/api/key-lock',
    '{"Content-Type":"application/json","X-App-Key":"test-key"}', 
    '{"keyId":"12345","action":"lock"}',
    200, '{"Content-Type":"application/json"}', 
    '{"success":true,"message":"Key locked successfully"}',
    NOW() - INTERVAL 30 MINUTE + INTERVAL 200 MILLISECOND, 200, true
),
(
    NOW() - INTERVAL 10 MINUTE, 'key-filling', '*************', 'POST', '/api/key-filling',
    '{"Content-Type":"application/json","X-App-Key":"invalid-key"}', 
    '{"keyId":"67890","action":"fill"}',
    401, '{"Content-Type":"application/json"}', 
    '{"success":false,"message":"Unauthorized"}',
    NOW() - INTERVAL 10 MINUTE + INTERVAL 50 MILLISECOND, 50, false
);
