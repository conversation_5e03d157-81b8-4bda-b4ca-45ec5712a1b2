<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ZF Security - 管理后台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .nav-links {
            display: flex;
            gap: 1rem;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #666;
            font-size: 1.1rem;
        }
        
        .logs-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }
        
        .filter-buttons {
            display: flex;
            gap: 0.5rem;
        }
        
        .filter-btn {
            padding: 0.5rem 1rem;
            border: 1px solid #ddd;
            background: white;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .filter-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .logs-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .logs-table th,
        .logs-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .logs-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        .logs-table tr:hover {
            background-color: #f8f9fa;
        }
        
        .status-success {
            color: #28a745;
            font-weight: bold;
        }
        
        .status-error {
            color: #dc3545;
            font-weight: bold;
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }
        
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 0.5rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .no-data {
            text-align: center;
            padding: 2rem;
            color: #666;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <h1>ZF Security - 管理后台</h1>
            </div>
            <div class="nav-links">
                <a href="/">首页</a>
                <span>欢迎，<span th:text="${user}">用户</span></span>
                <button class="logout-btn" onclick="logout()" style="background: rgba(255,255,255,0.2); color: white; border: none; padding: 0.5rem 1rem; border-radius: 5px; cursor: pointer;">退出</button>
            </div>
        </div>
    </header>
    
    <div class="container">
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" th:text="${totalLogs ?: 0}">0</div>
                <div class="stat-label">总请求数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" th:text="${failedLogs ?: 0}">0</div>
                <div class="stat-label">失败请求</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="successRate">-</div>
                <div class="stat-label">成功率</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="avgResponseTime">-</div>
                <div class="stat-label">平均响应时间</div>
            </div>
        </div>
        
        <div class="logs-section">
            <div class="section-header">
                <h3>接口调用日志</h3>
                <div class="filter-buttons">
                    <button class="filter-btn active" onclick="filterLogs('all')">全部</button>
                    <button class="filter-btn" onclick="filterLogs('key-filling')">Key Filling</button>
                    <button class="filter-btn" onclick="filterLogs('key-lock')">Key Lock</button>
                    <button class="filter-btn" onclick="filterLogs('failed')">失败记录</button>
                </div>
            </div>
            
            <div id="logsContainer">
                <div class="loading">
                    <div class="spinner"></div>
                    加载中...
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentFilter = 'all';
        
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                fetch('/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.href = '/login';
                    } else {
                        alert('退出失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('退出错误:', error);
                    alert('网络错误，请重试');
                });
            }
        }
        
        function filterLogs(type) {
            currentFilter = type;
            
            // 更新按钮状态
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 加载日志
            loadLogs(type);
        }
        
        function loadLogs(type = 'all') {
            const container = document.getElementById('logsContainer');
            container.innerHTML = '<div class="loading"><div class="spinner"></div>加载中...</div>';
            
            fetch(`/api/logs?type=${type}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayLogs(data.data);
                    } else {
                        container.innerHTML = '<div class="no-data">加载失败：' + data.message + '</div>';
                    }
                })
                .catch(error => {
                    console.error('加载日志失败:', error);
                    container.innerHTML = '<div class="no-data">网络错误，请重试</div>';
                });
        }
        
        function displayLogs(logs) {
            const container = document.getElementById('logsContainer');
            
            if (!logs || logs.length === 0) {
                container.innerHTML = '<div class="no-data">暂无数据</div>';
                return;
            }
            
            let html = `
                <table class="logs-table">
                    <thead>
                        <tr>
                            <th>时间</th>
                            <th>接口</th>
                            <th>客户端IP</th>
                            <th>方法</th>
                            <th>状态</th>
                            <th>耗时(ms)</th>
                            <th>结果</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            logs.forEach(log => {
                const requestTime = new Date(log.requestTime).toLocaleString();
                const status = log.success ? 
                    '<span class="status-success">成功</span>' : 
                    '<span class="status-error">失败</span>';
                
                html += `
                    <tr>
                        <td>${requestTime}</td>
                        <td>${log.interfaceName || '-'}</td>
                        <td>${log.clientIp || '-'}</td>
                        <td>${log.requestMethod || '-'}</td>
                        <td>${log.responseStatus || '-'}</td>
                        <td>${log.durationMs || '-'}</td>
                        <td>${status}</td>
                    </tr>
                `;
            });
            
            html += '</tbody></table>';
            container.innerHTML = html;
            
            // 计算统计信息
            calculateStats(logs);
        }
        
        function calculateStats(logs) {
            if (!logs || logs.length === 0) return;
            
            const totalLogs = logs.length;
            const successLogs = logs.filter(log => log.success).length;
            const successRate = ((successLogs / totalLogs) * 100).toFixed(1);
            
            const validDurations = logs.filter(log => log.durationMs != null).map(log => log.durationMs);
            const avgResponseTime = validDurations.length > 0 ? 
                (validDurations.reduce((a, b) => a + b, 0) / validDurations.length).toFixed(0) : '-';
            
            document.getElementById('successRate').textContent = successRate + '%';
            document.getElementById('avgResponseTime').textContent = avgResponseTime + 'ms';
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadLogs('all');
            
            // 每30秒自动刷新
            setInterval(() => {
                loadLogs(currentFilter);
            }, 30000);
        });
    </script>
</body>
</html>
