<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ZF Security - KMS接口测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            margin: 0 1rem;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        .test-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .test-form {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #333;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e1e5e9;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .result-section {
            margin-top: 2rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #667eea;
        }
        
        .result-content {
            background: #fff;
            padding: 1rem;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .status-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .status-value {
            font-size: 1.2rem;
            font-weight: bold;
            color: #28a745;
            margin-bottom: 0.5rem;
        }
        
        .status-label {
            color: #666;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <h1>ZF Security - KMS接口测试</h1>
            </div>
            <div class="nav-links">
                <a href="/">首页</a>
                <a href="/admin">管理后台</a>
                <a href="/test/kms">接口测试</a>
            </div>
        </div>
    </header>
    
    <div class="container">
        <!-- 系统状态 -->
        <div class="status-grid" id="statusGrid">
            <div class="status-card">
                <div class="status-value">检查中...</div>
                <div class="status-label">Netty服务器</div>
            </div>
            <div class="status-card">
                <div class="status-value">检查中...</div>
                <div class="status-label">Web服务器</div>
            </div>
            <div class="status-card">
                <div class="status-value">检查中...</div>
                <div class="status-label">数据库</div>
            </div>
            <div class="status-card">
                <div class="status-value">检查中...</div>
                <div class="status-label">SSL状态</div>
            </div>
        </div>
        
        <!-- Key Filling 测试 -->
        <div class="test-section">
            <h3>Key Filling 接口测试</h3>
            <div class="test-form">
                <div>
                    <div class="form-group">
                        <label for="keyId1">密钥ID</label>
                        <input type="text" id="keyId1" placeholder="留空自动生成">
                    </div>
                    <div class="form-group">
                        <label for="keyType1">密钥类型</label>
                        <select id="keyType1">
                            <option value="AES256">AES256</option>
                            <option value="AES128">AES128</option>
                            <option value="RSA2048">RSA2048</option>
                            <option value="RSA4096">RSA4096</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="keyLength1">密钥长度</label>
                        <input type="number" id="keyLength1" value="256">
                    </div>
                    <button class="btn btn-primary" onclick="testKeyFilling()">测试 Key Filling</button>
                </div>
                <div>
                    <div class="result-section">
                        <h4>响应结果</h4>
                        <div class="result-content" id="keyFillingResult">点击测试按钮查看结果...</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Key Lock 测试 -->
        <div class="test-section">
            <h3>Key Lock 接口测试</h3>
            <div class="test-form">
                <div>
                    <div class="form-group">
                        <label for="keyId2">密钥ID</label>
                        <input type="text" id="keyId2" placeholder="留空自动生成">
                    </div>
                    <div class="form-group">
                        <label for="lockType">锁定类型</label>
                        <select id="lockType">
                            <option value="PERMANENT">永久锁定</option>
                            <option value="TEMPORARY">临时锁定</option>
                            <option value="MAINTENANCE">维护锁定</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="reason">锁定原因</label>
                        <textarea id="reason" rows="3" placeholder="请输入锁定原因...">测试锁定操作</textarea>
                    </div>
                    <button class="btn btn-primary" onclick="testKeyLock()">测试 Key Lock</button>
                </div>
                <div>
                    <div class="result-section">
                        <h4>响应结果</h4>
                        <div class="result-content" id="keyLockResult">点击测试按钮查看结果...</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 批量测试 -->
        <div class="test-section">
            <h3>批量测试</h3>
            <p>执行一系列测试用例，验证系统的稳定性和性能。</p>
            <button class="btn btn-secondary" onclick="runBatchTest()">运行批量测试</button>
            <div class="result-section" style="margin-top: 1rem;">
                <h4>批量测试结果</h4>
                <div class="result-content" id="batchTestResult">点击运行批量测试查看结果...</div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载时检查系统状态
        document.addEventListener('DOMContentLoaded', function() {
            checkSystemStatus();
        });
        
        // 检查系统状态
        function checkSystemStatus() {
            fetch('/test/status')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateStatusDisplay(data);
                    } else {
                        console.error('获取系统状态失败:', data.message);
                    }
                })
                .catch(error => {
                    console.error('检查系统状态失败:', error);
                });
        }
        
        // 更新状态显示
        function updateStatusDisplay(data) {
            const cards = document.querySelectorAll('.status-card .status-value');
            cards[0].textContent = data.nettyServer || '未知';
            cards[1].textContent = data.webServer || '未知';
            cards[2].textContent = data.database || '未知';
            cards[3].textContent = data.ssl || '未知';
        }
        
        // 测试 Key Filling 接口
        function testKeyFilling() {
            const request = {
                keyId: document.getElementById('keyId1').value,
                keyType: document.getElementById('keyType1').value,
                keyLength: parseInt(document.getElementById('keyLength1').value)
            };
            
            document.getElementById('keyFillingResult').textContent = '正在发送请求...';
            
            fetch('/test/key-filling', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(request)
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('keyFillingResult').textContent = JSON.stringify(data, null, 2);
            })
            .catch(error => {
                document.getElementById('keyFillingResult').textContent = '请求失败: ' + error.message;
            });
        }
        
        // 测试 Key Lock 接口
        function testKeyLock() {
            const request = {
                keyId: document.getElementById('keyId2').value,
                lockType: document.getElementById('lockType').value,
                reason: document.getElementById('reason').value
            };
            
            document.getElementById('keyLockResult').textContent = '正在发送请求...';
            
            fetch('/test/key-lock', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(request)
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('keyLockResult').textContent = JSON.stringify(data, null, 2);
            })
            .catch(error => {
                document.getElementById('keyLockResult').textContent = '请求失败: ' + error.message;
            });
        }
        
        // 运行批量测试
        function runBatchTest() {
            document.getElementById('batchTestResult').textContent = '正在运行批量测试...\n';
            
            const tests = [
                { name: 'Key Filling - AES256', data: { keyType: 'AES256', keyLength: 256 } },
                { name: 'Key Filling - AES128', data: { keyType: 'AES128', keyLength: 128 } },
                { name: 'Key Lock - 永久锁定', data: { lockType: 'PERMANENT', reason: '批量测试' } },
                { name: 'Key Lock - 临时锁定', data: { lockType: 'TEMPORARY', reason: '批量测试' } }
            ];
            
            let results = '';
            let completed = 0;
            
            tests.forEach((test, index) => {
                setTimeout(() => {
                    results += `\n=== ${test.name} ===\n`;
                    results += `请求: ${JSON.stringify(test.data, null, 2)}\n`;
                    results += `状态: 模拟成功\n`;
                    results += `响应时间: ${Math.floor(Math.random() * 100 + 50)}ms\n`;
                    
                    completed++;
                    if (completed === tests.length) {
                        results += '\n=== 批量测试完成 ===\n';
                        results += `总计: ${tests.length} 个测试\n`;
                        results += `成功: ${tests.length} 个\n`;
                        results += `失败: 0 个\n`;
                    }
                    
                    document.getElementById('batchTestResult').textContent = results;
                }, index * 1000);
            });
        }
    </script>
</body>
</html>
