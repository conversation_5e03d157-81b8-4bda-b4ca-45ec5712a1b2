<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ZF Security - 首页</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo h1 {
            font-size: 1.8rem;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        .welcome-card {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .welcome-card h2 {
            color: #333;
            margin-bottom: 1rem;
        }
        
        .welcome-card p {
            color: #666;
            font-size: 1.1rem;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .feature-card h3 {
            color: #333;
            margin-bottom: 1rem;
        }
        
        .feature-card p {
            color: #666;
        }
        
        .admin-link {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            text-decoration: none;
            border-radius: 5px;
            font-weight: 500;
            transition: transform 0.2s;
        }
        
        .admin-link:hover {
            transform: translateY(-2px);
        }
        
        .status-info {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .status-item {
            text-align: center;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 5px;
        }
        
        .status-value {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .status-label {
            color: #666;
            margin-top: 0.5rem;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <h1>ZF Security</h1>
            </div>
            <div class="user-info">
                <span>欢迎，<span th:text="${user}">用户</span></span>
                <button class="logout-btn" onclick="logout()">退出</button>
            </div>
        </div>
    </header>
    
    <div class="container">
        <div class="welcome-card">
            <h2>接口中转服务管理系统</h2>
            <p>基于 Netty 的 mTLS 网络协议接口中转服务，提供安全可靠的接口代理功能</p>
        </div>
        
        <div class="status-info">
            <h3 style="margin-bottom: 1rem; color: #333;">系统状态</h3>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-value" id="nettyStatus">运行中</div>
                    <div class="status-label">Netty服务器</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="sslStatus">已启用</div>
                    <div class="status-label">SSL/TLS</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="dbStatus">已连接</div>
                    <div class="status-label">数据库</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="proxyStatus">正常</div>
                    <div class="status-label">代理服务</div>
                </div>
            </div>
        </div>
        
        <div class="features">
            <div class="feature-card">
                <div class="feature-icon">🔐</div>
                <h3>mTLS 安全通信</h3>
                <p>支持双向 SSL/TLS 认证，确保通信安全</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🔄</div>
                <h3>接口中转</h3>
                <p>支持 key-filling 和 key-lock 接口的安全中转</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">📊</div>
                <h3>日志记录</h3>
                <p>完整记录每次接口调用的详细日志信息</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">⚡</div>
                <h3>高性能</h3>
                <p>基于 Netty 框架，提供高并发处理能力</p>
            </div>
        </div>
        
        <div style="text-align: center;">
            <a href="/admin" class="admin-link">进入管理后台</a>
        </div>
    </div>

    <script>
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                fetch('/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.href = '/login';
                    } else {
                        alert('退出失败：' + data.message);
                    }
                })
                .catch(error => {
                    console.error('退出错误:', error);
                    alert('网络错误，请重试');
                });
            }
        }
        
        // 检查系统状态
        function checkSystemStatus() {
            // 这里可以添加实际的状态检查逻辑
            // 目前显示静态状态
        }
        
        // 页面加载时检查状态
        document.addEventListener('DOMContentLoaded', function() {
            checkSystemStatus();
        });
    </script>
</body>
</html>
