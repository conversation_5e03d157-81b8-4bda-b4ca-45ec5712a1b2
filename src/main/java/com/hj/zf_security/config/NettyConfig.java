package com.hj.zf_security.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Netty配置类
 */
@Configuration
@ConfigurationProperties(prefix = "netty.server")
public class NettyConfig {
    
    private int port = 8443;
    private SslConfig ssl = new SslConfig();
    
    public int getPort() {
        return port;
    }
    
    public void setPort(int port) {
        this.port = port;
    }
    
    public SslConfig getSsl() {
        return ssl;
    }
    
    public void setSsl(SslConfig ssl) {
        this.ssl = ssl;
    }
    
    public static class SslConfig {
        private boolean enabled = true;
        private String keystorePath = "classpath:certs/zf.pfx";
        private String keystorePassword = "hjdz@2025";
        private String keystoreType = "PKCS12";
        
        public boolean isEnabled() {
            return enabled;
        }
        
        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }
        
        public String getKeystorePath() {
            return keystorePath;
        }
        
        public void setKeystorePath(String keystorePath) {
            this.keystorePath = keystorePath;
        }
        
        public String getKeystorePassword() {
            return keystorePassword;
        }
        
        public void setKeystorePassword(String keystorePassword) {
            this.keystorePassword = keystorePassword;
        }
        
        public String getKeystoreType() {
            return keystoreType;
        }
        
        public void setKeystoreType(String keystoreType) {
            this.keystoreType = keystoreType;
        }
    }
}
