package com.hj.zf_security.netty;

import com.hj.zf_security.entity.ProxyLog;
import com.hj.zf_security.service.ProxyLogService;
import com.hj.zf_security.service.ProxyService;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelFutureListener;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.handler.codec.http.*;
import io.netty.util.CharsetUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.InetSocketAddress;
import java.util.HashMap;
import java.util.Map;

/**
 * Netty代理服务器处理器
 */
public class ProxyServer<PERSON>and<PERSON> extends SimpleChannelInboundHandler<FullHttpRequest> {

    private static final Logger logger = LoggerFactory.getLogger(ProxyServerHandler.class);

    private final ProxyService proxyService;
    private final ProxyLogService proxyLogService;
    private final ObjectMapper objectMapper;

    public ProxyServerHandler(ProxyService proxyService, ProxyLogService proxyLogService, ObjectMapper objectMapper) {
        this.proxyService = proxyService;
        this.proxyLogService = proxyLogService;
        this.objectMapper = objectMapper;
    }
    
    @Override
    protected void channelRead0(ChannelHandlerContext ctx, FullHttpRequest request) throws Exception {
        String clientIp = getClientIp(ctx);
        String uri = request.uri();
        String method = request.method().name();
        
        logger.info("收到请求: {} {} from {}", method, uri, clientIp);
        
        // 解析接口名称
        String interfaceName = parseInterfaceName(uri);
        if (interfaceName == null) {
            sendErrorResponse(ctx, HttpResponseStatus.NOT_FOUND, "接口不存在");
            return;
        }
        
        // 创建日志记录
        ProxyLog log = proxyLogService.createLog(interfaceName, clientIp);
        
        try {
            // 记录请求信息
            Map<String, String> requestHeaders = extractHeaders(request);
            String requestBody = request.content().toString(CharsetUtil.UTF_8);
            proxyLogService.logRequest(log, method, uri, requestHeaders, requestBody);
            
            // 执行代理转发
            String response = proxyService.forwardRequest(interfaceName, method, requestBody);
            
            // 发送响应
            sendSuccessResponse(ctx, response);
            
            // 记录成功响应
            Map<String, String> responseHeaders = new HashMap<>();
            responseHeaders.put("Content-Type", "application/json");
            proxyLogService.logResponse(log, 200, responseHeaders, response, true, null);
            
        } catch (Exception e) {
            logger.error("处理请求失败: {}", e.getMessage(), e);
            sendErrorResponse(ctx, HttpResponseStatus.INTERNAL_SERVER_ERROR, "服务器内部错误");
            proxyLogService.logError(log, "处理请求失败", e);
        }
    }
    
    /**
     * 解析接口名称
     */
    private String parseInterfaceName(String uri) {
        if (uri.startsWith("/api/")) {
            String path = uri.substring(5); // 去掉 "/api/"
            if (path.startsWith("key-filling")) {
                return "key-filling";
            } else if (path.startsWith("key-lock")) {
                return "key-lock";
            }
        }
        return null;
    }
    
    /**
     * 提取请求头
     */
    private Map<String, String> extractHeaders(FullHttpRequest request) {
        Map<String, String> headers = new HashMap<>();
        for (Map.Entry<String, String> entry : request.headers()) {
            headers.put(entry.getKey(), entry.getValue());
        }
        return headers;
    }
    
    /**
     * 获取客户端IP
     */
    private String getClientIp(ChannelHandlerContext ctx) {
        InetSocketAddress socketAddress = (InetSocketAddress) ctx.channel().remoteAddress();
        return socketAddress.getAddress().getHostAddress();
    }
    
    /**
     * 发送成功响应
     */
    private void sendSuccessResponse(ChannelHandlerContext ctx, String content) {
        ByteBuf buffer = Unpooled.copiedBuffer(content, CharsetUtil.UTF_8);
        FullHttpResponse response = new DefaultFullHttpResponse(
                HttpVersion.HTTP_1_1, HttpResponseStatus.OK, buffer);
        
        response.headers().set(HttpHeaderNames.CONTENT_TYPE, "application/json; charset=UTF-8");
        response.headers().set(HttpHeaderNames.CONTENT_LENGTH, buffer.readableBytes());
        response.headers().set(HttpHeaderNames.CONNECTION, HttpHeaderValues.CLOSE);
        
        ctx.writeAndFlush(response).addListener(ChannelFutureListener.CLOSE);
    }
    
    /**
     * 发送错误响应
     */
    private void sendErrorResponse(ChannelHandlerContext ctx, HttpResponseStatus status, String message) {
        try {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", true);
            errorResponse.put("message", message);
            errorResponse.put("code", status.code());
            
            String content = objectMapper.writeValueAsString(errorResponse);
            ByteBuf buffer = Unpooled.copiedBuffer(content, CharsetUtil.UTF_8);
            
            FullHttpResponse response = new DefaultFullHttpResponse(
                    HttpVersion.HTTP_1_1, status, buffer);
            
            response.headers().set(HttpHeaderNames.CONTENT_TYPE, "application/json; charset=UTF-8");
            response.headers().set(HttpHeaderNames.CONTENT_LENGTH, buffer.readableBytes());
            response.headers().set(HttpHeaderNames.CONNECTION, HttpHeaderValues.CLOSE);
            
            ctx.writeAndFlush(response).addListener(ChannelFutureListener.CLOSE);
        } catch (Exception e) {
            logger.error("发送错误响应失败", e);
            ctx.close();
        }
    }
    
    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        logger.error("连接异常", cause);
        ctx.close();
    }
}
