package com.hj.zf_security.netty;

import com.hj.zf_security.config.NettyConfig;
import com.hj.zf_security.util.SslContextUtil;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.codec.http.HttpObjectAggregator;
import io.netty.handler.codec.http.HttpServerCodec;
import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.SslHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

/**
 * Netty服务器
 */
@Component
public class NettyServer {
    
    private static final Logger logger = LoggerFactory.getLogger(NettyServer.class);
    
    @Autowired
    private NettyConfig nettyConfig;
    
    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private com.hj.zf_security.service.ProxyService proxyService;

    @Autowired
    private com.hj.zf_security.service.ProxyLogService proxyLogService;

    @Autowired
    private com.fasterxml.jackson.databind.ObjectMapper objectMapper;
    
    private EventLoopGroup bossGroup;
    private EventLoopGroup workerGroup;
    private Channel serverChannel;
    
    @PostConstruct
    public void start() {
        new Thread(this::doStart).start();
    }
    
    private void doStart() {
        bossGroup = new NioEventLoopGroup(1);
        workerGroup = new NioEventLoopGroup();
        
        try {
            ServerBootstrap bootstrap = new ServerBootstrap();
            bootstrap.group(bossGroup, workerGroup)
                    .channel(NioServerSocketChannel.class)
                    .option(ChannelOption.SO_BACKLOG, 128)
                    .childOption(ChannelOption.SO_KEEPALIVE, true)
                    .childHandler(new ChannelInitializer<SocketChannel>() {
                        @Override
                        protected void initChannel(SocketChannel ch) throws Exception {
                            ChannelPipeline pipeline = ch.pipeline();
                            
                            // SSL处理器
                            if (nettyConfig.getSsl().isEnabled()) {
                                SslContext sslContext = SslContextUtil.createServerSslContext(
                                        nettyConfig.getSsl().getKeystorePath(),
                                        nettyConfig.getSsl().getKeystorePassword(),
                                        nettyConfig.getSsl().getKeystoreType()
                                );
                                pipeline.addLast(sslContext.newHandler(ch.alloc()));
                            }
                            
                            // HTTP编解码器
                            pipeline.addLast(new HttpServerCodec());
                            
                            // HTTP消息聚合器
                            pipeline.addLast(new HttpObjectAggregator(65536));
                            
                            // 自定义处理器
                            ProxyServerHandler handler = new ProxyServerHandler(proxyService, proxyLogService, objectMapper);
                            pipeline.addLast(handler);
                        }
                    });
            
            // 绑定端口并启动服务器
            ChannelFuture future = bootstrap.bind(nettyConfig.getPort()).sync();
            serverChannel = future.channel();
            
            logger.info("Netty服务器启动成功，端口: {}, SSL: {}", 
                       nettyConfig.getPort(), nettyConfig.getSsl().isEnabled());
            
            // 等待服务器关闭
            serverChannel.closeFuture().sync();
            
        } catch (Exception e) {
            logger.error("Netty服务器启动失败", e);
        } finally {
            shutdown();
        }
    }
    
    @PreDestroy
    public void shutdown() {
        logger.info("正在关闭Netty服务器...");
        
        if (serverChannel != null) {
            serverChannel.close();
        }
        
        if (workerGroup != null) {
            workerGroup.shutdownGracefully();
        }
        
        if (bossGroup != null) {
            bossGroup.shutdownGracefully();
        }
        
        logger.info("Netty服务器已关闭");
    }
}
