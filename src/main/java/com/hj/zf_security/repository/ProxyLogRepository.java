package com.hj.zf_security.repository;

import com.hj.zf_security.entity.ProxyLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 代理日志数据访问层
 */
@Repository
public interface ProxyLogRepository extends JpaRepository<ProxyLog, Long> {
    
    /**
     * 根据接口名称查询日志
     */
    List<ProxyLog> findByInterfaceName(String interfaceName);
    
    /**
     * 根据时间范围查询日志
     */
    @Query("SELECT p FROM ProxyLog p WHERE p.requestTime BETWEEN :startTime AND :endTime ORDER BY p.requestTime DESC")
    List<ProxyLog> findByRequestTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                           @Param("endTime") LocalDateTime endTime);
    
    /**
     * 根据客户端IP查询日志
     */
    List<ProxyLog> findByClientIp(String clientIp);
    
    /**
     * 查询失败的请求
     */
    List<ProxyLog> findBySuccessFalseOrderByRequestTimeDesc();
    
    /**
     * 统计接口调用次数
     */
    @Query("SELECT p.interfaceName, COUNT(p) FROM ProxyLog p GROUP BY p.interfaceName")
    List<Object[]> countByInterfaceName();
}
