package com.hj.zf_security.service;

import com.hj.zf_security.entity.ProxyLog;
import com.hj.zf_security.repository.ProxyLogRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 代理日志服务
 */
@Service
public class ProxyLogService {
    
    private static final Logger logger = LoggerFactory.getLogger(ProxyLogService.class);
    
    @Autowired
    private ProxyLogRepository proxyLogRepository;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * 创建新的代理日志
     */
    public ProxyLog createLog(String interfaceName, String clientIp) {
        ProxyLog log = new ProxyLog(interfaceName, clientIp);
        return proxyLogRepository.save(log);
    }
    
    /**
     * 记录请求信息
     */
    public void logRequest(ProxyLog log, String method, String url, 
                          Map<String, String> headers, String body) {
        try {
            log.setRequestMethod(method);
            log.setRequestUrl(url);
            log.setRequestHeaders(objectMapper.writeValueAsString(headers));
            log.setRequestBody(body);
            proxyLogRepository.save(log);
        } catch (Exception e) {
            logger.error("记录请求信息失败", e);
        }
    }
    
    /**
     * 记录响应信息
     */
    public void logResponse(ProxyLog log, int status, Map<String, String> headers, 
                           String body, boolean success, String errorMessage) {
        try {
            log.setResponseTime(LocalDateTime.now());
            log.setResponseStatus(status);
            log.setResponseHeaders(objectMapper.writeValueAsString(headers));
            log.setResponseBody(body);
            log.setSuccess(success);
            log.setErrorMessage(errorMessage);
            
            // 计算请求耗时
            if (log.getRequestTime() != null) {
                long duration = java.time.Duration.between(log.getRequestTime(), log.getResponseTime()).toMillis();
                log.setDurationMs(duration);
            }
            
            proxyLogRepository.save(log);
        } catch (Exception e) {
            logger.error("记录响应信息失败", e);
        }
    }
    
    /**
     * 记录错误信息
     */
    public void logError(ProxyLog log, String errorMessage, Exception exception) {
        try {
            log.setResponseTime(LocalDateTime.now());
            log.setSuccess(false);
            log.setErrorMessage(errorMessage + (exception != null ? ": " + exception.getMessage() : ""));
            
            // 计算请求耗时
            if (log.getRequestTime() != null) {
                long duration = java.time.Duration.between(log.getRequestTime(), log.getResponseTime()).toMillis();
                log.setDurationMs(duration);
            }
            
            proxyLogRepository.save(log);
        } catch (Exception e) {
            logger.error("记录错误信息失败", e);
        }
    }
    
    /**
     * 查询所有日志
     */
    public List<ProxyLog> getAllLogs() {
        return proxyLogRepository.findAll();
    }
    
    /**
     * 根据接口名称查询日志
     */
    public List<ProxyLog> getLogsByInterface(String interfaceName) {
        return proxyLogRepository.findByInterfaceName(interfaceName);
    }
    
    /**
     * 查询失败的请求
     */
    public List<ProxyLog> getFailedLogs() {
        return proxyLogRepository.findBySuccessFalseOrderByRequestTimeDesc();
    }
    
    /**
     * 根据时间范围查询日志
     */
    public List<ProxyLog> getLogsByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        return proxyLogRepository.findByRequestTimeBetween(startTime, endTime);
    }
}
