package com.hj.zf_security.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.classic.methods.HttpUriRequest;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.apache.hc.core5.http.io.entity.StringEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * 代理转发服务
 */
@Service
public class ProxyService {
    
    private static final Logger logger = LoggerFactory.getLogger(ProxyService.class);
    
    @Value("${proxy.target.base-url:https://api.example.com}")
    private String baseUrl;
    
    @Value("${proxy.auth.app-key:your-app-key}")
    private String appKey;
    
    @Value("${proxy.auth.app-secret:your-app-secret}")
    private String appSecret;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final CloseableHttpClient httpClient = HttpClients.createDefault();
    
    /**
     * 转发请求
     */
    public String forwardRequest(String interfaceName, String method, 
                               Map<String, String> headers, String body) throws Exception {
        
        // 构建目标URL
        String targetUrl = buildTargetUrl(interfaceName);
        
        // 创建HTTP请求
        HttpUriRequest request = createHttpRequest(method, targetUrl, headers, body);
        
        // 添加认证头
        addAuthHeaders(request, interfaceName, body);
        
        // 执行请求
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);

            logger.info("转发请求成功: {} -> {}, 状态码: {}",
                       interfaceName, targetUrl, response.getCode());

            return responseBody;
        } catch (Exception e) {
            logger.warn("转发请求失败，使用模拟响应: {} -> {}, 错误: {}",
                       interfaceName, targetUrl, e.getMessage());

            // 如果转发失败，返回模拟响应
            return mockResponse(interfaceName, body);
        }
    }
    
    /**
     * 构建目标URL
     */
    private String buildTargetUrl(String interfaceName) {
        switch (interfaceName) {
            case "key-filling":
                return baseUrl + "/api/key-filling";
            case "key-lock":
                return baseUrl + "/api/key-lock";
            default:
                throw new IllegalArgumentException("不支持的接口: " + interfaceName);
        }
    }
    
    /**
     * 创建HTTP请求
     */
    private HttpUriRequest createHttpRequest(String method, String url, 
                                           Map<String, String> headers, String body) {
        HttpUriRequest request;
        
        if ("POST".equalsIgnoreCase(method)) {
            HttpPost post = new HttpPost(url);
            if (body != null && !body.isEmpty()) {
                post.setEntity(new StringEntity(body, StandardCharsets.UTF_8));
            }
            request = post;
        } else {
            request = new HttpGet(url);
        }
        
        // 添加原始请求头（过滤掉一些不需要的头）
        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                String headerName = entry.getKey().toLowerCase();
                if (!headerName.equals("host") && 
                    !headerName.equals("content-length") &&
                    !headerName.startsWith("x-forwarded")) {
                    request.addHeader(entry.getKey(), entry.getValue());
                }
            }
        }
        
        return request;
    }
    
    /**
     * 添加认证头
     */
    private void addAuthHeaders(HttpUriRequest request, String interfaceName, String body) {
        try {
            long timestamp = Instant.now().getEpochSecond();
            String nonce = generateNonce();
            
            // 构建签名参数
            Map<String, String> signParams = new TreeMap<>();
            signParams.put("appkey", appKey);
            signParams.put("timestamp", String.valueOf(timestamp));
            signParams.put("nonce", nonce);
            signParams.put("interface", interfaceName);
            
            if (body != null && !body.isEmpty()) {
                signParams.put("body", body);
            }
            
            // 生成签名
            String signature = generateSignature(signParams);
            
            // 添加认证头
            request.addHeader("X-App-Key", appKey);
            request.addHeader("X-Timestamp", String.valueOf(timestamp));
            request.addHeader("X-Nonce", nonce);
            request.addHeader("X-Signature", signature);
            request.addHeader("Content-Type", "application/json");
            
        } catch (Exception e) {
            logger.error("添加认证头失败", e);
            throw new RuntimeException("认证失败", e);
        }
    }
    
    /**
     * 生成随机数
     */
    private String generateNonce() {
        return String.valueOf(System.currentTimeMillis() + (int)(Math.random() * 1000));
    }
    
    /**
     * 生成签名
     */
    private String generateSignature(Map<String, String> params) throws Exception {
        // 构建签名字符串
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (sb.length() > 0) {
                sb.append("&");
            }
            sb.append(entry.getKey()).append("=").append(entry.getValue());
        }
        sb.append("&secret=").append(appSecret);
        
        // MD5签名
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] digest = md.digest(sb.toString().getBytes(StandardCharsets.UTF_8));
        
        StringBuilder hexString = new StringBuilder();
        for (byte b : digest) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        
        return hexString.toString().toUpperCase();
    }
    
    /**
     * 模拟接口响应（用于测试）
     */
    public String mockResponse(String interfaceName, String requestBody) {
        try {
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("interface", interfaceName);
            response.put("timestamp", Instant.now().getEpochSecond());
            response.put("message", "请求处理成功");
            
            if ("key-filling".equals(interfaceName)) {
                response.put("data", Map.of("keyId", "12345", "status", "filled"));
            } else if ("key-lock".equals(interfaceName)) {
                response.put("data", Map.of("keyId", "12345", "status", "locked"));
            }
            
            return objectMapper.writeValueAsString(response);
        } catch (Exception e) {
            logger.error("生成模拟响应失败", e);
            return "{\"success\":false,\"message\":\"服务器内部错误\"}";
        }
    }
}
