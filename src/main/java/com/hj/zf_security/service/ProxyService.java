package com.hj.zf_security.service;

import cn.hutool.core.util.RandomUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.classic.methods.HttpUriRequest;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.utils.Hex;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.apache.hc.core5.http.io.entity.StringEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.CompletableFuture;

/**
 * 代理转发服务
 */
@Service
public class ProxyService {

    private static final Logger logger = LoggerFactory.getLogger(ProxyService.class);

    @Value("${proxy.target.base-url:https://kms.example.com}")
    private String baseUrl;

    @Value("${proxy.auth.app-key:HJ_KMS_APP_KEY}")
    private String appKey;

    @Value("${proxy.auth.app-secret:HJ_KMS_APP_SECRET_2025}")
    private String appSecret;

    @Value("${proxy.interfaces.key-filling.path:/api/v1/key/filling}")
    private String keyFillingPath;

    @Value("${proxy.interfaces.key-filling.method:POST}")
    private String keyFillingMethod;

    @Value("${proxy.interfaces.key-lock.path:/api/v1/key/lock}")
    private String keyLockPath;

    @Value("${proxy.interfaces.key-lock.method:POST}")
    private String keyLockMethod;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final CloseableHttpClient httpClient = HttpClients.createDefault();

    /**
     * 转发请求
     */
    public String forwardRequest(String interfaceName, String method, String body) throws Exception {

        // 生成时间戳
        Long timestamp = System.currentTimeMillis();

        String esAuthNonce = RandomUtil.randomString(16);

        Map<String, String> headers = new HashMap<>();
        headers.put("es-auth-appkey", appKey);
        headers.put("es-auth-nonce", esAuthNonce);
        headers.put("es-auth-timestamp", String.valueOf(timestamp));

        // 构建目标URL
        String targetUrl = switch (interfaceName) {
            case "key-filling" -> baseUrl + keyFillingPath;
            case "key-lock" -> baseUrl + keyLockPath;
            default -> throw new IllegalArgumentException("不支持的接口: " + interfaceName);
        };

        // 生成签名数据

        //todo
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("uid", "ui24030800001");
        requestBody.put("pid", "pid2025030301");
        String signatureData = generateSignature("POST", targetUrl, headers, appSecret, requestBody.toString());
        headers.put("es-auth-signature", signatureData);

        // 创建HTTP请求

        //todo

        body = requestBody.toString();

        HttpUriRequest request = createHttpRequest(method, targetUrl, headers, body);

        // 执行请求
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);

            logger.info("转发请求成功: {} -> {}, 状态码: {}",
                    interfaceName, targetUrl, response.getCode());

            return responseBody;
        } catch (Exception e) {
            logger.warn("转发请求失败，使用模拟响应: {} -> {}, 错误: {}",
                    interfaceName, targetUrl, e.getMessage());

            // 如果转发失败，返回模拟响应
            return mockResponse(interfaceName, body);
        }
    }

    /**
     * 生成签名数据字符串
     */
    public String generateSignature(String method, String url, Map<String, String> header, String appSecret, String body) {
        String hashHex = "";
        StringBuilder httpStr = new StringBuilder();
        httpStr.append(method).append(url)
                .append("es-auth-appkey:").append(header.get("es-auth-appkey"))
                .append("es-auth-nonce:").append(header.get("es-auth-nonce"))
                .append("es-auth-timestamp:").append(header.get("es-auth-timestamp"))
                .append("json=").append(body).append(appSecret);
        String urlCodeStr;
        try {
            urlCodeStr = URLEncoder.encode(httpStr.toString(), "UTF-8");
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(urlCodeStr.getBytes(StandardCharsets.UTF_8));
            hashHex = Hex.encodeHexString(hash);
        } catch (Exception e) {
            logger.error("生成签名数据字符串失败", e);
        }
        return hashHex;
    }

    /**
     * 创建HTTP请求
     */
    private HttpUriRequest createHttpRequest(String method, String url,
                                             Map<String, String> headers, String body) {
        HttpUriRequest request;

        if ("POST".equalsIgnoreCase(method)) {
            HttpPost post = new HttpPost(url);
            if (body != null && !body.isEmpty()) {
                post.setEntity(new StringEntity(body, StandardCharsets.UTF_8));
            }
            request = post;
        } else {
            request = new HttpGet(url);
        }

        // 添加原始请求头（过滤掉一些不需要的头）
        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                String headerName = entry.getKey().toLowerCase();
                if (!headerName.equals("host") &&
                        !headerName.equals("content-length") &&
                        !headerName.startsWith("x-forwarded")) {
                    request.addHeader(entry.getKey(), entry.getValue());
                }
            }
        }

        return request;
    }

    /**
     * 模拟KMS接口响应（用于测试）
     */
    public String mockResponse(String interfaceName, String requestBody) {
        try {
            Map<String, Object> response = new HashMap<>();

            // 解析请求体
            Map<String, Object> requestData = new HashMap<>();
            if (requestBody != null && !requestBody.isEmpty()) {
                try {
                    requestData = objectMapper.readValue(requestBody, Map.class);
                } catch (Exception e) {
                    logger.warn("解析请求体失败: {}", e.getMessage());
                }
            }

            // KMS标准响应格式
            response.put("code", "0000");
            response.put("message", "SUCCESS");
            response.put("timestamp", Instant.now().getEpochSecond());
            response.put("requestId", generateRequestId());

            // 根据接口类型生成不同的响应数据
            Map<String, Object> data = new HashMap<>();

            if ("key-filling".equals(interfaceName)) {
                String keyId = (String) requestData.getOrDefault("keyId", "KMS_" + System.currentTimeMillis());
                data.put("keyId", keyId);
                data.put("status", "FILLED");
                data.put("fillTime", Instant.now().toString());
                data.put("keyType", requestData.getOrDefault("keyType", "AES256"));
                data.put("keyLength", requestData.getOrDefault("keyLength", 256));
                response.put("message", "密钥填充成功");

            } else if ("key-lock".equals(interfaceName)) {
                String keyId = (String) requestData.getOrDefault("keyId", "KMS_" + System.currentTimeMillis());
                data.put("keyId", keyId);
                data.put("status", "LOCKED");
                data.put("lockTime", Instant.now().toString());
                data.put("lockType", requestData.getOrDefault("lockType", "PERMANENT"));
                response.put("message", "密钥锁定成功");
            }

            response.put("data", data);

            return objectMapper.writeValueAsString(response);

        } catch (Exception e) {
            logger.error("生成模拟响应失败", e);

            // 错误响应格式
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", "9999");
            errorResponse.put("message", "SYSTEM_ERROR");
            errorResponse.put("timestamp", Instant.now().getEpochSecond());
            errorResponse.put("requestId", generateRequestId());
            errorResponse.put("error", "服务器内部错误: " + e.getMessage());

            try {
                return objectMapper.writeValueAsString(errorResponse);
            } catch (Exception ex) {
                return "{\"code\":\"9999\",\"message\":\"SYSTEM_ERROR\",\"error\":\"JSON序列化失败\"}";
            }
        }
    }

    /**
     * 生成请求ID
     */
    private String generateRequestId() {
        return "REQ_" + System.currentTimeMillis() + "_" + (int) (Math.random() * 10000);
    }
}
