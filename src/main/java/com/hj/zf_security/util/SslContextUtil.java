package com.hj.zf_security.util;

import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.SslContextBuilder;
import io.netty.handler.ssl.util.SelfSignedCertificate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.TrustManagerFactory;
import java.io.InputStream;
import java.security.KeyStore;

/**
 * SSL上下文工具类
 */
public class SslContextUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(SslContextUtil.class);
    
    /**
     * 创建服务器端SSL上下文
     */
    public static SslContext createServerSslContext(String keystorePath, String keystorePassword, String keystoreType) {
        try {
            // 如果证书文件不存在，使用自签名证书
            if (!isKeystoreExists(keystorePath)) {
                logger.warn("证书文件不存在: {}, 使用自签名证书", keystorePath);
                return createSelfSignedSslContext();
            }
            
            // 加载密钥库
            KeyStore keyStore = KeyStore.getInstance(keystoreType);
            try (InputStream keystoreStream = getKeystoreStream(keystorePath)) {
                keyStore.load(keystoreStream, keystorePassword.toCharArray());
            }
            
            // 初始化密钥管理器
            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            keyManagerFactory.init(keyStore, keystorePassword.toCharArray());
            
            // 初始化信任管理器（用于客户端证书验证）
            TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
            trustManagerFactory.init(keyStore);
            
            // 创建SSL上下文
            return SslContextBuilder.forServer(keyManagerFactory)
                    .trustManager(trustManagerFactory)
                    .clientAuth(io.netty.handler.ssl.ClientAuth.OPTIONAL) // 可选的客户端认证
                    .build();
                    
        } catch (Exception e) {
            logger.error("创建SSL上下文失败，使用自签名证书", e);
            return createSelfSignedSslContext();
        }
    }
    
    /**
     * 创建自签名证书的SSL上下文
     */
    private static SslContext createSelfSignedSslContext() {
        try {
            SelfSignedCertificate ssc = new SelfSignedCertificate();
            return SslContextBuilder.forServer(ssc.certificate(), ssc.privateKey()).build();
        } catch (Exception e) {
            logger.error("创建自签名SSL上下文失败", e);
            throw new RuntimeException("无法创建SSL上下文", e);
        }
    }
    
    /**
     * 检查密钥库文件是否存在
     */
    private static boolean isKeystoreExists(String keystorePath) {
        try {
            if (keystorePath.startsWith("classpath:")) {
                Resource resource = new ClassPathResource(keystorePath.substring("classpath:".length()));
                return resource.exists();
            } else {
                return new java.io.File(keystorePath).exists();
            }
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 获取密钥库输入流
     */
    private static InputStream getKeystoreStream(String keystorePath) throws Exception {
        if (keystorePath.startsWith("classpath:")) {
            Resource resource = new ClassPathResource(keystorePath.substring("classpath:".length()));
            return resource.getInputStream();
        } else {
            return new java.io.FileInputStream(keystorePath);
        }
    }
}
