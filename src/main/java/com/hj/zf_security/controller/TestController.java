package com.hj.zf_security.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpSession;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器 - 用于测试KMS接口
 */
@RestController
@RequestMapping("/test")
public class TestController {
    
    private static final Logger logger = LoggerFactory.getLogger(TestController.class);
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * 测试页面
     */
    @GetMapping("/kms")
    public String testPage(HttpSession session) {
        String user = (String) session.getAttribute("user");
        if (user == null) {
            return "redirect:/login";
        }
        return "test-kms";
    }
    
    /**
     * 测试key-filling接口
     */
    @PostMapping("/key-filling")
    @ResponseBody
    public Map<String, Object> testKeyFilling(@RequestBody Map<String, Object> request, HttpSession session) {
        String user = (String) session.getAttribute("user");
//        if (user == null) {
//            return createErrorResponse("未登录");
//        }
        
        try {
            // 构建测试请求
            Map<String, Object> kmsRequest = new HashMap<>();
            kmsRequest.put("keyId", request.getOrDefault("keyId", "TEST_KEY_" + System.currentTimeMillis()));
            kmsRequest.put("keyType", request.getOrDefault("keyType", "AES256"));
            kmsRequest.put("keyLength", request.getOrDefault("keyLength", 256));
            kmsRequest.put("operator", user);
            kmsRequest.put("timestamp", System.currentTimeMillis());
            
            // 发送到Netty服务器
            String response = sendToNettyServer("/api/key-filling", kmsRequest);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "测试请求发送成功");
            result.put("request", kmsRequest);
            result.put("response", response);
            
            return result;
            
        } catch (Exception e) {
            logger.error("测试key-filling接口失败", e);
            return createErrorResponse("测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试key-lock接口
     */
    @PostMapping("/key-lock")
    @ResponseBody
    public Map<String, Object> testKeyLock(@RequestBody Map<String, Object> request, HttpSession session) {
        String user = (String) session.getAttribute("user");
        if (user == null) {
            return createErrorResponse("未登录");
        }
        
        try {
            // 构建测试请求
            Map<String, Object> kmsRequest = new HashMap<>();
            kmsRequest.put("keyId", request.getOrDefault("keyId", "TEST_KEY_" + System.currentTimeMillis()));
            kmsRequest.put("lockType", request.getOrDefault("lockType", "PERMANENT"));
            kmsRequest.put("reason", request.getOrDefault("reason", "测试锁定"));
            kmsRequest.put("operator", user);
            kmsRequest.put("timestamp", System.currentTimeMillis());
            
            // 发送到Netty服务器
            String response = sendToNettyServer("/api/key-lock", kmsRequest);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "测试请求发送成功");
            result.put("request", kmsRequest);
            result.put("response", response);
            
            return result;
            
        } catch (Exception e) {
            logger.error("测试key-lock接口失败", e);
            return createErrorResponse("测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 发送请求到Netty服务器
     */
    private String sendToNettyServer(String path, Map<String, Object> data) throws Exception {
        // 这里可以实现HTTP客户端调用Netty服务器
        // 为了简化，直接返回模拟响应
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", "0000");
        response.put("message", "SUCCESS");
        response.put("timestamp", System.currentTimeMillis());
        response.put("requestId", "REQ_" + System.currentTimeMillis());
        response.put("data", data);
        
        return objectMapper.writeValueAsString(response);
    }
    
    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", message);
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }
    
    /**
     * 获取系统状态
     */
    @GetMapping("/status")
    @ResponseBody
    public Map<String, Object> getSystemStatus(HttpSession session) {
        String user = (String) session.getAttribute("user");
        if (user == null) {
            return createErrorResponse("未登录");
        }
        
        Map<String, Object> status = new HashMap<>();
        status.put("success", true);
        status.put("nettyServer", "运行中 (端口: 8443)");
        status.put("webServer", "运行中 (端口: 8081)");
        status.put("database", "H2内存数据库 - 已连接");
        status.put("ssl", "已启用 (自签名证书)");
        status.put("timestamp", System.currentTimeMillis());
        
        return status;
    }
}
