package com.hj.zf_security.controller;

import com.hj.zf_security.service.ProxyLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.Map;

/**
 * 登录控制器
 */
@Controller
public class LoginController {
    
    private static final Logger logger = LoggerFactory.getLogger(LoginController.class);
    
    // 硬编码的用户信息
    private final Map<String, String> users = Map.of(
        "admin", "admin123",
        "user", "user123"
    );
    
    @Autowired
    private ProxyLogService proxyLogService;
    
    /**
     * 显示登录页面
     */
    @GetMapping("/login")
    public String loginPage() {
        return "login";
    }
    
    /**
     * 处理登录请求
     */
    @PostMapping("/login")
    @ResponseBody
    public Map<String, Object> login(@RequestParam String username, 
                                   @RequestParam String password,
                                   HttpSession session) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 验证用户名和密码
            if (users.containsKey(username) && users.get(username).equals(password)) {
                session.setAttribute("user", username);
                result.put("success", true);
                result.put("message", "登录成功");
                logger.info("用户登录成功: {}", username);
            } else {
                result.put("success", false);
                result.put("message", "用户名或密码错误");
                logger.warn("用户登录失败: {}", username);
            }
        } catch (Exception e) {
            logger.error("登录处理异常", e);
            result.put("success", false);
            result.put("message", "系统错误");
        }
        
        return result;
    }
    
    /**
     * 退出登录
     */
    @PostMapping("/logout")
    @ResponseBody
    public Map<String, Object> logout(HttpSession session) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String username = (String) session.getAttribute("user");
            session.invalidate();
            result.put("success", true);
            result.put("message", "退出成功");
            logger.info("用户退出登录: {}", username);
        } catch (Exception e) {
            logger.error("退出登录异常", e);
            result.put("success", false);
            result.put("message", "系统错误");
        }
        
        return result;
    }
    
    /**
     * 首页
     */
    @GetMapping("/")
    public String index(HttpSession session, Model model) {
        String user = (String) session.getAttribute("user");
        if (user == null) {
            return "redirect:/login";
        }
        
        model.addAttribute("user", user);
        return "index";
    }
    
    /**
     * 管理页面
     */
    @GetMapping("/admin")
    public String admin(HttpSession session, Model model) {
        String user = (String) session.getAttribute("user");
        if (user == null) {
            return "redirect:/login";
        }
        
        // 获取日志统计信息
        try {
            model.addAttribute("user", user);
            model.addAttribute("totalLogs", proxyLogService.getAllLogs().size());
            model.addAttribute("failedLogs", proxyLogService.getFailedLogs().size());
            model.addAttribute("recentLogs", proxyLogService.getAllLogs().stream()
                    .sorted((a, b) -> b.getRequestTime().compareTo(a.getRequestTime()))
                    .limit(10)
                    .toList());
        } catch (Exception e) {
            logger.error("获取管理页面数据失败", e);
        }
        
        return "admin";
    }
    
    /**
     * 获取日志数据API
     */
    @GetMapping("/api/logs")
    @ResponseBody
    public Map<String, Object> getLogs(HttpSession session,
                                     @RequestParam(defaultValue = "all") String type) {
        Map<String, Object> result = new HashMap<>();
        
        String user = (String) session.getAttribute("user");
        if (user == null) {
            result.put("success", false);
            result.put("message", "未登录");
            return result;
        }
        
        try {
            switch (type) {
                case "failed":
                    result.put("data", proxyLogService.getFailedLogs());
                    break;
                case "key-filling":
                    result.put("data", proxyLogService.getLogsByInterface("key-filling"));
                    break;
                case "key-lock":
                    result.put("data", proxyLogService.getLogsByInterface("key-lock"));
                    break;
                default:
                    result.put("data", proxyLogService.getAllLogs());
                    break;
            }
            result.put("success", true);
        } catch (Exception e) {
            logger.error("获取日志数据失败", e);
            result.put("success", false);
            result.put("message", "获取数据失败");
        }
        
        return result;
    }
}
